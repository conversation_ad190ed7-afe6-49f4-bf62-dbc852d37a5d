"""Demo parser functions for extracting data from CS2 demo files."""

import time
from collections.abc import Callable
from pathlib import Path
from typing import TYPE_CHECKING

from anyio import to_thread
from demoparser2 import DemoParser as DP2Parser
from result import Err, Ok, Result

from .async_utils import get_thread_limiter
from .logging_config import get_logger
from .models import DemoData, MapData, PlayerData, RoundData

if TYPE_CHECKING:
    import pandas as pd
else:
    try:
        import pandas as pd
    except ImportError:
        pd = None  # type: ignore[assignment]

logger = get_logger(__name__)


async def parse_demo(
    file_path: str, *, progress_callback: Callable[[str, float], None] | None = None
) -> Result[DemoData, str]:
    """Parse a demo file and extract complete match data.

    Args:
        file_path: Path to the demo file
        progress_callback: Optional callback for progress updates (stage, progress)

    Returns:
        Result containing <PERSON><PERSON><PERSON><PERSON> on success or error message on failure
    """
    logger.info("Starting to parse demo file", file_path=file_path)
    start_time = time.time()

    # Validate file existence
    validation_result = await _validate_demo_file(file_path)
    if validation_result.is_err():
        return Err(validation_result.unwrap_err())

    if progress_callback:
        progress_callback("Validating file", 0.1)

    # Initialize parser
    parser_result = await _initialize_parser(file_path)
    if parser_result.is_err():
        return Err(parser_result.unwrap_err())

    if progress_callback:
        progress_callback("Parser initialized", 0.2)

    # Extract match data
    demo_data_result = await _extract_match_data_async(
        parser_result.unwrap(), progress_callback
    )
    if demo_data_result.is_err():
        return Err(demo_data_result.unwrap_err())

    parse_time = time.time() - start_time
    logger.info("Successfully parsed demo", file_path=file_path, parse_time=parse_time)

    if progress_callback:
        progress_callback("Parsing complete", 1.0)

    return Ok(demo_data_result.unwrap())


async def _validate_demo_file(file_path: str) -> Result[None, str]:
    """Validate that the demo file exists and is accessible.

    Args:
        file_path: Path to the demo file

    Returns:
        Result with None on success or error message on failure
    """
    path = Path(file_path)
    limiter = get_thread_limiter()

    # Check if file exists
    if not await to_thread.run_sync(path.exists, limiter=limiter):
        return Err(f"Demo file not found: {file_path}")

    # Check if file is a file
    if not await to_thread.run_sync(path.is_file, limiter=limiter):
        return Err(f"Path is not a file: {file_path}")

    # Check file size
    try:
        stat = await to_thread.run_sync(path.stat, limiter=limiter)
        if stat.st_size == 0:
            return Err(f"Demo file is empty: {file_path}")
    except OSError as e:
        return Err(f"Cannot access demo file: {file_path}: {e}")

    return Ok(None)


async def _initialize_parser(file_path: str) -> Result[DP2Parser, str]:
    """Initialize the demo parser.

    Args:
        file_path: Path to the demo file

    Returns:
        Result containing initialized parser or error message
    """
    try:
        limiter = get_thread_limiter()
        parser = await to_thread.run_sync(DP2Parser, file_path, limiter=limiter)
        return Ok(parser)
    except (ValueError, OSError) as e:
        return Err(f"Failed to initialize parser: {e!s}")


async def _extract_match_data_async(
    parser: DP2Parser, progress_callback: Callable[[str, float], None] | None = None
) -> Result[DemoData, str]:
    """Extract match data from a parsed demo asynchronously.

    Args:
        parser: Initialized DemoParser2 instance
        progress_callback: Optional callback for progress updates

    Returns:
        Result containing DemoData or error message
    """
    logger.info("Extracting match data from demo")

    if progress_callback:
        progress_callback("Starting data extraction", 0.3)

    # Parse player data
    player_df_result = await _parse_player_data(parser)
    if player_df_result.is_err():
        return Err(player_df_result.unwrap_err())

    if progress_callback:
        progress_callback("Player data extracted", 0.5)

    # Parse round events
    round_events_result = await _parse_round_events(parser)
    if round_events_result.is_err():
        return Err(round_events_result.unwrap_err())

    if progress_callback:
        progress_callback("Round data extracted", 0.7)

    # Parse game events
    game_events_result = await _parse_game_events(parser)
    if game_events_result.is_err():
        return Err(game_events_result.unwrap_err())

    if progress_callback:
        progress_callback("Game events extracted", 0.8)

    # Extract structured data using functional composition
    return await _build_demo_data(
        player_df_result.unwrap(),
        round_events_result.unwrap(),
        game_events_result.unwrap(),
        progress_callback,
    )


async def _parse_player_data(parser: DP2Parser) -> Result["pd.DataFrame", str]:
    """Parse player data from the demo.

    Returns:
        Result containing player dataframe or error message.
    """
    try:
        limiter = get_thread_limiter()
        player_df = await to_thread.run_sync(
            lambda: parser.parse_ticks(["player_name", "player_steamid", "team_num"]),
            limiter=limiter,
        )
        return Ok(player_df)
    except (ValueError, RuntimeError) as e:
        return Err(f"Failed to parse player data: {e!s}")


async def _parse_round_events(parser: DP2Parser) -> Result["pd.DataFrame", str]:
    """Parse round end events from the demo.

    Returns:
        Result containing round events dataframe or error message.
    """
    try:
        limiter = get_thread_limiter()
        round_events_df = await to_thread.run_sync(
            lambda: parser.parse_event("round_end", other=["total_rounds_played"]),
            limiter=limiter,
        )
        return Ok(round_events_df)
    except (ValueError, RuntimeError) as e:
        return Err(f"Failed to parse round events: {e!s}")


async def _parse_game_events(parser: DP2Parser) -> Result["pd.DataFrame", str]:
    """Parse game start events from the demo.

    Returns:
        Result containing game events dataframe or error message.
    """
    try:
        limiter = get_thread_limiter()
        game_events_df = await to_thread.run_sync(
            lambda: parser.parse_event("game_start"), limiter=limiter
        )
        return Ok(game_events_df)
    except (ValueError, RuntimeError) as e:
        return Err(f"Failed to parse game events: {e!s}")


async def _build_demo_data(
    player_df: "pd.DataFrame",
    round_events_df: "pd.DataFrame",
    game_events_df: "pd.DataFrame",
    progress_callback: Callable[[str, float], None] | None = None,
) -> Result[DemoData, str]:
    """Build DemoData from parsed dataframes using functional composition.

    Returns:
        Result containing DemoData or error message.
    """
    # Extract map information
    map_result = await _extract_map_name_async(game_events_df)
    if map_result.is_err():
        return Err(map_result.unwrap_err())

    map_name = map_result.unwrap()
    map_info = MapData(name=map_name)

    # Extract player information
    players_result = await _extract_players_async(player_df)
    if players_result.is_err():
        return Err(players_result.unwrap_err())

    players = players_result.unwrap()

    # Extract round information
    rounds_result = await _extract_rounds_async(round_events_df)
    if rounds_result.is_err():
        return Err(rounds_result.unwrap_err())

    rounds = rounds_result.unwrap()

    # Create match info
    match_info: dict[str, str | int | float | bool] = {
        "total_rounds": len(rounds),
        "map_name": map_name,
        "total_players": len(players),
    }

    logger.info(
        "Successfully extracted match data",
        map_name=map_name,
        total_rounds=len(rounds),
        total_players=len(players),
    )

    if progress_callback:
        progress_callback("Data extraction complete", 0.9)

    return Ok(
        DemoData(
            map_info=map_info,
            players=players,
            rounds=rounds,
            match_info=match_info,
        )
    )


# Type ignore comments are used here because pandas Series types are complex and dynamic
# These functions safely handle the type conversion with proper error handling
def _safe_get_string(obj: "pd.Series | dict[str, object]", key: str, default: str = "") -> str:  # type: ignore
    """Safely extract string value from pandas Series/dict-like object."""
    try:
        value = obj.get(key) if hasattr(obj, "get") else getattr(obj, key, None)
        return str(value) if value is not None else default
    except (AttributeError, KeyError, TypeError):
        return default


def _safe_get_int(obj: "pd.Series | dict[str, object]", key: str, default: int = 0) -> int:  # type: ignore
    """Safely extract integer value from pandas Series/dict-like object."""
    try:
        value = obj.get(key) if hasattr(obj, "get") else getattr(obj, key, None)
        if value is None:
            return default
        return int(float(str(value)))  # Convert via string to handle various types
    except (AttributeError, KeyError, TypeError, ValueError):
        return default


async def _extract_map_name_async(game_events_df: "pd.DataFrame") -> Result[str, str]:
    """Extract map name from game events dataframe asynchronously.

    Args:
        game_events_df: DataFrame containing game events

    Returns:
        Result containing map name or error message
    """

    def extract_map_name() -> str:
        if game_events_df.empty:
            return "unknown"

        if "map_name" not in game_events_df.columns:
            return "unknown"

        if len(game_events_df) == 0:
            return "unknown"

        # Get map name from first game event
        map_name_value = game_events_df["map_name"].iloc[0]  # type: ignore
        return str(map_name_value) if map_name_value is not None else "unknown"

    limiter = get_thread_limiter()
    result = await to_thread.run_sync(extract_map_name, limiter=limiter)
    return Ok(result)


async def _extract_players_async(
    player_df: "pd.DataFrame",
) -> Result[list[PlayerData], str]:
    """Extract player data from dataframe asynchronously.

    Args:
        player_df: DataFrame containing player data

    Returns:
        Result containing list of PlayerData or error message
    """

    def extract_players() -> list[PlayerData]:
        players: list[PlayerData] = []

        if player_df.empty:
            logger.warning("No player data found in demo")
            return players

        # Get unique players
        unique_players = player_df.drop_duplicates(subset=["player_steamid"])

        for _, row in unique_players.iterrows():  # type: ignore
            # Use safe extraction helpers to handle pandas row access
            team_num = _safe_get_int(row, "team_num", 0)  # type: ignore
            team_name = "CT" if team_num == 3 else "T"

            steam_id = _safe_get_string(row, "player_steamid", "")  # type: ignore
            name = _safe_get_string(row, "player_name", "Unknown")  # type: ignore

            player = PlayerData(
                steam_id=steam_id,
                name=name,
                team=team_name,
            )
            players.append(player)

        return players

    limiter = get_thread_limiter()
    result = await to_thread.run_sync(extract_players, limiter=limiter)
    return Ok(result)


async def _extract_rounds_async(
    round_events_df: "pd.DataFrame",
) -> Result[list[RoundData], str]:
    """Extract round data from dataframe asynchronously.

    Args:
        round_events_df: DataFrame containing round end events

    Returns:
        Result containing list of RoundData or error message
    """

    def extract_rounds() -> list[RoundData]:
        rounds: list[RoundData] = []

        if round_events_df.empty:
            logger.warning("No round data found in demo")
            return rounds

        for i, row in round_events_df.iterrows():  # type: ignore
            # Convert index to int for round number calculation
            round_number = int(i) + 1 if isinstance(i, int | float) else 1

            # Use safe extraction helpers to handle pandas row access
            winner = _safe_get_string(row, "winner", "Unknown")  # type: ignore
            win_reason = _safe_get_string(row, "reason", "Unknown")  # type: ignore
            ct_score = _safe_get_int(row, "ct_score", 0)  # type: ignore
            t_score = _safe_get_int(row, "t_score", 0)  # type: ignore

            round_data = RoundData(
                round_number=round_number,
                winner=winner,
                win_reason=win_reason,
                ct_score=ct_score,
                t_score=t_score,
            )
            rounds.append(round_data)

        return rounds

    limiter = get_thread_limiter()
    result = await to_thread.run_sync(extract_rounds, limiter=limiter)
    return Ok(result)


# Functional composition helpers
def compose_results[T, U, E](
    result1: Result[T, E], result2: Result[U, E]
) -> Result[tuple[T, U], E]:
    """Compose two Results into a tuple Result.

    Returns:
        Result containing tuple of both values or first error encountered.
    """
    if result1.is_err():
        return Err(result1.unwrap_err())
    if result2.is_err():
        return Err(result2.unwrap_err())
    return Ok((result1.unwrap(), result2.unwrap()))


def sequence_results[T, E](results: list[Result[T, E]]) -> Result[list[T], E]:
    """Convert a list of Results to a Result of list.

    Returns:
        Result containing list of all values or first error encountered.
    """
    values: list[T] = []
    for result in results:
        if result.is_err():
            return Err(result.unwrap_err())
        values.append(result.unwrap())
    return Ok(values)
